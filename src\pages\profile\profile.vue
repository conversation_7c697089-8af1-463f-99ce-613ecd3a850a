<template>
  <view class="profile-page">
    <NavBar title="我的" :show-back="false" />

    <scroll-view class="content" scroll-y>
      <!-- 上部分：个人信息 -->
      <view class="profile-header">
        <view class="user-info-card">
          <view class="avatar-section" :class="{ 'not-logged-in': !userInfo.isLoggedIn, 'logged-in': userInfo.isLoggedIn }">
            <!-- 未登录状态 -->
            <template v-if="!userInfo.isLoggedIn">
              <view class="welcome-section">
                <text class="welcome-text">嗨，多肉冥想的新朋友</text>
              </view>
              <view class="login-btn" @click="login">
                <text class="login-text">立即登录</text>
              </view>
            </template>

            <!-- 已登录状态 -->
            <template v-else>
              <image class="avatar" :src="userInfo.avatarUrl" mode="aspectFill" />
              <view class="user-basic">
                <text class="username">{{ userInfo.nickname }}</text>
              </view>
            </template>
          </view>

          <view class="stats-grid">
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.succulentCount }}</text>
              <text class="stat-label">我的多肉</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-number">Lv.{{ userInfo.meditationLevel }}</text>
              <text class="stat-label">冥想等级</text>
            </view>
            <view class="stat-divider"></view>
            <view class="stat-item">
              <text class="stat-number">{{ userInfo.totalDays }}</text>
              <text class="stat-label">坚持天数</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 中部分：功能入口和数据展示 -->
      <view class="profile-middle">
        <!-- 会员开通入口 -->
        <view class="vip-card" @click="openVip">
          <view class="vip-content">
            <view class="vip-info">
              <text class="vip-title">开通会员</text>
              <text class="vip-desc">解锁更多多肉品种和冥想内容</text>
            </view>
            <view class="vip-icon">
              <text class="crown-icon">👑</text>
            </view>
          </view>
        </view>

        <!-- 功能入口网格 -->
        <view class="function-grid">
          <view class="function-item" @click="goToMyFavorites">
            <view class="function-icon favorite-bg">
              <text class="icon">❤️</text>
            </view>
            <text class="function-label">我的收藏</text>
          </view>

          <view class="function-item" @click="goToSucculentSpace">
            <view class="function-icon succulent-bg">
              <text class="icon">🌱</text>
            </view>
            <text class="function-label">多肉空间</text>
          </view>

          <view class="function-item" @click="goToMyData">
            <view class="function-icon data-bg">
              <text class="icon">📊</text>
            </view>
            <text class="function-label">我的数据</text>
          </view>
        </view>

        <!-- 数据概览卡片 -->
        <view class="data-overview-card">
          <view class="card-header">
            <text class="card-title">本周数据</text>
            <text class="more-btn" @click="goToMyData">查看更多</text>
          </view>
          <view class="data-items">
            <view class="data-item">
              <text class="data-label">冥想时长</text>
              <text class="data-value">{{ weekData.meditationTime }}分钟</text>
            </view>
            <view class="data-item">
              <text class="data-label">获得能量</text>
              <text class="data-value">{{ weekData.energy }}</text>
            </view>
            <view class="data-item">
              <text class="data-label">完成任务</text>
              <text class="data-value">{{ weekData.completedTasks }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 下部分：菜单列表 -->
      <view class="profile-bottom">
        <view class="menu-card">
          <view class="menu-item" @click="followWechat">
            <view class="menu-icon">
              <text class="icon">📱</text>
            </view>
            <text class="menu-label">关注公众号</text>
            <text class="menu-arrow">></text>
          </view>

          <view class="menu-divider"></view>

          <view class="menu-item" @click="goToAbout">
            <view class="menu-icon">
              <text class="icon">ℹ️</text>
            </view>
            <text class="menu-label">关于我们</text>
            <text class="menu-arrow">></text>
          </view>

          <view class="menu-divider"></view>

          <view class="menu-item" @click="goToFeedback">
            <view class="menu-icon">
              <text class="icon">💬</text>
            </view>
            <text class="menu-label">意见反馈</text>
            <text class="menu-arrow">></text>
          </view>

          <view class="menu-divider"></view>

          <view class="menu-item" @click="goToSettings">
            <view class="menu-icon">
              <text class="icon">⚙️</text>
            </view>
            <text class="menu-label">设置</text>
            <text class="menu-arrow">></text>
          </view>
        </view>
      </view>
    </scroll-view>

    <TabBar />
  </view>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import NavBar from '@/components/NavBar.vue'
import TabBar from '@/components/TabBar.vue'
import {onLoad, onShow} from '@dcloudio/uni-app'
import { useAuth } from '@/composables/useAuth'

// 用户信息
const userInfo = ref({
  avatarUrl: "https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132",
  nickname: '多肉爱好者',
  id: 'USER001',
  succulentCount: 12,
  meditationLevel: 5,
  totalDays: 28,
  isLoggedIn: false
})
const auth = useAuth()

// 本周数据
const weekData = ref({
  meditationTime: 180,
  energy: 1250,
  completedTasks: 15
})

// 登录方法 - 必须在用户点击事件中调用
const login = () => {
  uni.showLoading({ title: '登录中...' })

  // 必须在用户点击事件中同步调用登录方法
  auth.login().then((user) => {
    // 更新本地用户信息显示
    userInfo.value = {
      ...userInfo.value,
      avatarUrl: user.avatar_url,
      nickname: user.nickname,
      isLoggedIn: true,
      id: user.id.toString(),
      meditationLevel: user.meditation_level,
      totalDays: user.streak_days
    }

    uni.hideLoading()
    uni.showToast({
      title: '登录成功',
      icon: 'success'
    })
  }).catch((error) => {
    uni.hideLoading()
    console.error('登录失败:', error)
    uni.showToast({
      title: error instanceof Error ? error.message : '登录失败',
      icon: 'none'
    })
  })
}

// 功能方法
const openVip = () => {
  uni.showToast({ title: '开通会员功能开发中', icon: 'none' })
}

const goToMyFavorites = () => {
  uni.navigateTo({ url: '/pages/favorites/favorites' })
}

const goToSucculentSpace = () => {
  uni.navigateTo({ url: '/pages/succulent/space' })
}

const goToMyData = () => {
  uni.navigateTo({ url: '/pages/data/index' })
}

const followWechat = () => {
  uni.showModal({
    title: '关注公众号',
    content: '请在微信中搜索"多肉冥想"关注我们的公众号',
    showCancel: false
  })
}

const goToAbout = () => {
  uni.navigateTo({ url: '/pages/about/index' })
}

const goToFeedback = () => {
  uni.navigateTo({ url: '/pages/feedback/index' })
}

const goToSettings = () => {
  uni.navigateTo({ url: '/pages/settings/index' })
}


// 检查并更新用户登录状态的函数
const checkUserLoginStatus = () => {
  // 从store中获取用户信息
  if (auth.isLoggedIn && auth.userInfo) {
    userInfo.value = {
      ...userInfo.value,
      avatarUrl: auth.userInfo.avatar_url || userInfo.value.avatarUrl,
      nickname: auth.userInfo.nickname || userInfo.value.nickname,
      isLoggedIn: true,
      id: auth.userInfo.id.toString(),
      meditationLevel: auth.userInfo.meditation_level,
      totalDays: auth.userInfo.streak_days
    }
  } else {
    // 如果没有登录，保持未登录状态
    userInfo.value = {
      ...userInfo.value,
      isLoggedIn: false
    }
  }
}

onLoad(() => {
  checkUserLoginStatus()
})

onShow(() => {
  // 页面显示时重新检查登录状态
  checkUserLoginStatus()
})

</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F9F0 0%, #F7FAFC 40%, #FFFFFF 100%);
  position: relative;
}

.profile-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 50%, rgba(240, 249, 240, 0.03) 100%);
  pointer-events: none;
  z-index: 0;
}

.content {
  padding-top: 176rpx;
  padding-bottom: 150rpx;
  height: calc(100vh - 326rpx);
  position: relative;
  z-index: 1;
}

/* 上部分：个人信息 */
.profile-header {
  padding: 40rpx 24rpx 56rpx;
}

.user-info-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx 36rpx;
  box-shadow: 0 12rpx 40rpx rgba(127, 176, 105, 0.06), 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
}

.user-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.avatar-section {
  display: flex;
  align-items: center;
  margin-bottom: 56rpx;
  position: relative;
  z-index: 1;
}

/* 未登录状态的布局 */
.avatar-section.not-logged-in {
  justify-content: space-between;
}

/* 已登录状态的布局 */
.avatar-section.logged-in {
  justify-content: flex-start;
}

.welcome-section {
  flex: 1;
}

.welcome-text {
  font-size: 40rpx;
  font-weight: 700;
  color: #2D3748;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
}

.avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  margin-right: 36rpx;
  box-shadow: 0 8rpx 24rpx rgba(127, 176, 105, 0.15), 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  border: 3rpx solid rgba(255, 255, 255, 0.8);
}

.user-basic {
  flex: 1;
  position: relative;
  z-index: 1;
}

.username {
  display: block;
  font-size: 40rpx;
  font-weight: 700;
  color: #2D3748;
  margin-bottom: 12rpx;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
}

.user-id {
  font-size: 26rpx;
  color: #718096;
  font-weight: 500;
  opacity: 0.9;
}

.login-btn {
  padding: 20rpx 32rpx;
  background: linear-gradient(135deg, #7FB069 0%, #A8D08D 100%);
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 180rpx;
  box-shadow: 0 8rpx 24rpx rgba(127, 176, 105, 0.3), 0 4rpx 12rpx rgba(127, 176, 105, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.login-btn:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 12rpx 32rpx rgba(127, 176, 105, 0.4), 0 6rpx 16rpx rgba(127, 176, 105, 0.3);
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:active {
  transform: translateY(-2rpx) scale(0.98);
}

.login-text {
  font-size: 30rpx;
  color: #FFFFFF;
  font-weight: 600;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5rpx;
  position: relative;
  z-index: 1;
}

.stats-grid {
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: relative;
  z-index: 1;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-2rpx);
}

.stat-number {
  font-size: 44rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(127, 176, 105, 0.1);
}

.stat-label {
  font-size: 26rpx;
  color: #4A5568;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.stat-divider {
  width: 2rpx;
  height: 72rpx;
  background: linear-gradient(180deg, transparent 0%, rgba(226, 232, 240, 0.8) 20%, rgba(226, 232, 240, 1) 50%, rgba(226, 232, 240, 0.8) 80%, transparent 100%);
}

/* 中部分：功能入口 */
.profile-middle {
  padding: 0 24rpx 56rpx;
}

.vip-card {
  background: linear-gradient(135deg, #F4A261 0%, #E76F51 100%);
  border-radius: 32rpx;
  padding: 40rpx 36rpx;
  margin-bottom: 56rpx;
  box-shadow: 0 12rpx 40rpx rgba(244, 162, 97, 0.15), 0 4rpx 16rpx rgba(231, 111, 81, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.vip-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  pointer-events: none;
  z-index: 0;
}

.vip-card:hover {
  transform: translateY(-4rpx) scale(1.02);
  box-shadow: 0 16rpx 48rpx rgba(244, 162, 97, 0.2), 0 6rpx 20rpx rgba(231, 111, 81, 0.15);
}

.vip-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  z-index: 1;
}

.vip-info {
  flex: 1;
}

.vip-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 12rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.5rpx;
}

.vip-desc {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 500;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
  letter-spacing: 0.3rpx;
  line-height: 1.4;
}

.vip-icon {
  width: 88rpx;
  height: 88rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  backdrop-filter: blur(10rpx);
}

.crown-icon {
  font-size: 64rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.function-grid {
  display: flex;
  gap: 20rpx;
  margin-bottom: 56rpx;
}

.function-item {
  flex: 1;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  padding: 52rpx 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.08), 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.function-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.function-item:hover {
  transform: translateY(-6rpx) scale(1.03);
  box-shadow: 0 16rpx 48rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.function-item:active {
  transform: translateY(-3rpx) scale(0.98);
}

.function-icon {
  width: 104rpx;
  height: 104rpx;
  border-radius: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  position: relative;
  z-index: 1;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.function-item:hover .function-icon {
  transform: scale(1.05);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.2);
}

.function-icon .icon {
  font-size: 52rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.favorite-bg {
  background: linear-gradient(135deg, #FF6B9D 0%, #FF8E9B 100%);
}

.succulent-bg {
  background: linear-gradient(135deg, #7FB069 0%, #A8D08D 100%);
}

.data-bg {
  background: linear-gradient(135deg, #F4A261 0%, #E76F51 100%);
}

.function-label {
  font-size: 26rpx;
  color: #4A5568;
  text-align: center;
  font-weight: 600;
  letter-spacing: 0.3rpx;
  position: relative;
  z-index: 1;
}

.data-overview-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 40rpx 36rpx;
  box-shadow: 0 12rpx 40rpx rgba(127, 176, 105, 0.06), 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
}

.data-overview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 40rpx;
  position: relative;
  z-index: 1;
}

.card-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #2D3748;
  letter-spacing: 0.5rpx;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
}

.more-btn {
  font-size: 26rpx;
  color: #7fb069;
  font-weight: 600;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  position: relative;
}

.more-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.1) 0%, rgba(168, 208, 141, 0.05) 100%);
  border-radius: 16rpx;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.more-btn:hover::before {
  opacity: 1;
}

.data-items {
  display: flex;
  justify-content: space-around;
  position: relative;
  z-index: 1;
}

.data-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.data-item:hover {
  transform: translateY(-2rpx);
}

.data-label {
  font-size: 26rpx;
  color: #718096;
  margin-bottom: 12rpx;
  font-weight: 500;
  letter-spacing: 0.3rpx;
}

.data-value {
  font-size: 40rpx;
  font-weight: 700;
  background: linear-gradient(135deg, #7fb069 0%, #A8D08D 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2rpx 4rpx rgba(127, 176, 105, 0.1);
}

/* 下部分：菜单列表 */
.profile-bottom {
  padding: 0 24rpx 56rpx;
}

.menu-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(247, 250, 252, 0.95) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(127, 176, 105, 0.06), 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  position: relative;
}

.menu-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
  z-index: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 52rpx 36rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
  z-index: 1;
}

.menu-item:hover {
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.05) 0%, rgba(168, 208, 141, 0.03) 100%);
  transform: translateX(4rpx);
}

.menu-item:active {
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 100%);
  transform: translateX(2rpx) scale(0.98);
}

.menu-icon {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 36rpx;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.1) 0%, rgba(168, 208, 141, 0.05) 100%);
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.menu-item:hover .menu-icon {
  transform: scale(1.05);
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.15) 0%, rgba(168, 208, 141, 0.08) 100%);
}

.menu-icon .icon {
  font-size: 40rpx;
  filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
}

.menu-label {
  flex: 1;
  font-size: 30rpx;
  color: #2D3748;
  font-weight: 600;
  letter-spacing: 0.3rpx;
}

.menu-arrow {
  font-size: 36rpx;
  color: #A0AEC0;
  transition: all 0.3s ease;
}

.menu-item:hover .menu-arrow {
  color: #7fb069;
  transform: translateX(4rpx);
}

.menu-divider {
  height: 1rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(226, 232, 240, 0.6) 20%, rgba(226, 232, 240, 1) 50%, rgba(226, 232, 240, 0.6) 80%, transparent 100%);
  margin: 0 36rpx;
}
</style>